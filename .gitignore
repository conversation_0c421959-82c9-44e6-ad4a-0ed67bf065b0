# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Cache directories
.cache/
.tmp/
.temp/
.turbo/

# Database files
*.db
*.sqlite
*.sqlite3

# API Keys and Secrets
.env.secret
secrets.json
config/secrets.js

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/
test-results.xml

# Backup files
*.backup
*.bak
*.tmp

# Local development
.local/
dev-dist/

# Storybook
.out
.storybook-out
storybook-static

# PWA files
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# Sentry
.sentryclirc

# Local Netlify folder
.netlify

# Application specific
# Add your custom ignores here
