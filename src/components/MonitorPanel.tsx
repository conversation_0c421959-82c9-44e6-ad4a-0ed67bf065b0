'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PriceData } from '@/types/price';

interface MonitorPanelProps {
  prices: {
    binance: PriceData | null;
    backpack: PriceData | null;
    lighter: PriceData | null;
  };
}

export function MonitorPanel({ prices }: MonitorPanelProps) {
  // 计算币安 vs Lighter 价差
  const calculateBinanceLighterSpread = () => {
    if (!prices.binance || !prices.lighter) return null;

    const spread = prices.binance.price - prices.lighter.price;
    const spreadPercent = ((spread / prices.lighter.price) * 100);

    return {
      absolute: spread.toFixed(2),
      percentage: spreadPercent.toFixed(2),
      isPositive: spread >= 0
    };
  };

  // 计算 Backpack vs Lighter 价差
  const calculateBackpackLighterSpread = () => {
    if (!prices.backpack || !prices.lighter) return null;

    const spread = prices.backpack.price - prices.lighter.price;
    const spreadPercent = ((spread / prices.lighter.price) * 100);

    return {
      absolute: spread.toFixed(2),
      percentage: spreadPercent.toFixed(2),
      isPositive: spread >= 0
    };
  };

  const binanceLighterSpread = calculateBinanceLighterSpread();
  const backpackLighterSpread = calculateBackpackLighterSpread();

  // 获取交易所状态
  const getExchangeStatus = (exchange: 'binance' | 'backpack' | 'lighter') => {
    const price = prices[exchange];
    return {
      connected: price?.connected || false,
      price: price?.price || 0,
      name: exchange === 'binance' ? '币安' : exchange === 'backpack' ? 'Backpack' : 'Lighter'
    };
  };

  return (
    <Card className="w-full border-2 border-gray-300">
      <CardContent className="p-6">
        <div className="text-sm text-gray-600 mb-6">监控</div>

        {/* 第一行：交易所状态 */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          {/* 币安 */}
          <Button
            variant="outline"
            className="h-16 flex flex-col items-center justify-center border-gray-300 hover:bg-gray-50"
          >
            <span className="text-xs text-gray-600 mb-1">币安</span>
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${getExchangeStatus('binance').connected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-xs font-medium">BTC/USDC</span>
            </div>
          </Button>

          {/* Backpack */}
          <Button
            variant="outline"
            className="h-16 flex flex-col items-center justify-center border-gray-300 hover:bg-gray-50"
          >
            <span className="text-xs text-gray-600 mb-1">Backpack</span>
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${getExchangeStatus('backpack').connected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-xs font-medium">BTC/USDC</span>
            </div>
          </Button>

          {/* Lighter */}
          <Button
            variant="outline"
            className="h-16 flex flex-col items-center justify-center border-gray-300 hover:bg-gray-50"
          >
            <span className="text-xs text-gray-600 mb-1">Lighter</span>
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${getExchangeStatus('lighter').connected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-xs font-medium">BTC/USD</span>
            </div>
          </Button>
        </div>

        {/* 第二行：价差监控 */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {/* 币安 vs Lighter 价差 */}
          <Button
            variant="outline"
            className="h-16 flex flex-col items-center justify-center border-gray-300 hover:bg-gray-50"
          >
            <span className="text-xs text-gray-600 mb-1">币安-Lighter</span>
            <div>
              {binanceLighterSpread ? (
                <div className="text-center">
                  <div className={`text-xs font-medium ${binanceLighterSpread.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                    {binanceLighterSpread.isPositive ? '+' : ''}${binanceLighterSpread.absolute}
                  </div>
                  <div className={`text-xs ${binanceLighterSpread.isPositive ? 'text-green-500' : 'text-red-500'}`}>
                    ({binanceLighterSpread.isPositive ? '+' : ''}{binanceLighterSpread.percentage}%)
                  </div>
                </div>
              ) : (
                <span className="text-xs text-gray-400">--</span>
              )}
            </div>
          </Button>

          {/* Backpack vs Lighter 价差 */}
          <Button
            variant="outline"
            className="h-16 flex flex-col items-center justify-center border-gray-300 hover:bg-gray-50"
          >
            <span className="text-xs text-gray-600 mb-1">Backpack-Lighter</span>
            <div>
              {backpackLighterSpread ? (
                <div className="text-center">
                  <div className={`text-xs font-medium ${backpackLighterSpread.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                    {backpackLighterSpread.isPositive ? '+' : ''}${backpackLighterSpread.absolute}
                  </div>
                  <div className={`text-xs ${backpackLighterSpread.isPositive ? 'text-green-500' : 'text-red-500'}`}>
                    ({backpackLighterSpread.isPositive ? '+' : ''}{backpackLighterSpread.percentage}%)
                  </div>
                </div>
              ) : (
                <span className="text-xs text-gray-400">--</span>
              )}
            </div>
          </Button>

          {/* 条件 */}
          <Button
            variant="outline"
            className="h-16 flex flex-col items-center justify-center border-gray-300 hover:bg-gray-50"
          >
            <span className="text-xs text-gray-600 mb-1">条件(大于小于)</span>
            <Badge variant="secondary" className="text-xs">
              未设置
            </Badge>
          </Button>

          {/* 数值 */}
          <Button
            variant="outline"
            className="h-16 flex flex-col items-center justify-center border-gray-300 hover:bg-gray-50"
          >
            <span className="text-xs text-gray-600 mb-1">数值</span>
            <div>
              <span className="text-xs font-medium">--</span>
            </div>
          </Button>

          {/* 是否启用 */}
          <Button
            className="h-16 flex flex-col items-center justify-center bg-gray-800 hover:bg-gray-700 text-white rounded-lg"
          >
            <span className="text-xs mb-1">是否启用</span>
            <Badge variant="secondary" className="text-xs bg-white text-gray-800">
              启用
            </Badge>
          </Button>
        </div>

        {/* 底部状态信息 */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center gap-4">
              <span>监控状态: 运行中</span>
              <span>检查频率: 实时</span>
            </div>
            <div className="flex items-center gap-2">
              <span>最后检查: {new Date().toLocaleTimeString('zh-CN')}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
