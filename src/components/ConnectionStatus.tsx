'use client';

import { Badge } from '@/components/ui/badge';
import { Wifi, WifiOff, AlertCircle } from 'lucide-react';

interface ConnectionStatusProps {
  binanceConnected: boolean;
  backpackConnected: boolean;
  lighterConnected: boolean;
}

export function ConnectionStatus({ 
  binanceConnected, 
  backpackConnected, 
  lighterConnected 
}: ConnectionStatusProps) {
  const totalConnections = 3;
  const activeConnections = [binanceConnected, backpackConnected, lighterConnected].filter(Boolean).length;
  
  const getStatusText = () => {
    if (activeConnections === totalConnections) return '全部连接';
    if (activeConnections > 0) return `${activeConnections}/${totalConnections} 连接`;
    return '连接断开';
  };

  const getStatusIcon = () => {
    if (activeConnections === totalConnections) return <Wifi className="h-3 w-3" />;
    if (activeConnections > 0) return <AlertCircle className="h-3 w-3" />;
    return <WifiOff className="h-3 w-3" />;
  };

  return (
    <div className="flex items-center gap-4">
      <Badge 
        variant={activeConnections === totalConnections ? "default" : "destructive"}
        className="flex items-center gap-1"
      >
        {getStatusIcon()}
        {getStatusText()}
      </Badge>
      
      <div className="flex items-center gap-2 text-sm text-gray-500">
        <div className="flex items-center gap-1">
          <div className={`w-2 h-2 rounded-full ${binanceConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span>币安</span>
        </div>
        <div className="flex items-center gap-1">
          <div className={`w-2 h-2 rounded-full ${backpackConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span>Backpack</span>
        </div>
        <div className="flex items-center gap-1">
          <div className={`w-2 h-2 rounded-full ${lighterConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span>Lighter</span>
        </div>
      </div>
    </div>
  );
}
