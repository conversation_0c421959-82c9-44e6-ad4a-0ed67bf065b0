'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { SpreadMonitorRuleComponent } from './SpreadMonitorRule';
import { SpreadMonitorRule, PLATFORMS, OPERATORS } from '@/types/monitor';
import { Plus, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

interface SpreadMonitorManagerProps {
  binancePrice?: number;
  backpackPrice?: number;
  lighterPrice?: number;
}

export function SpreadMonitorManager({ binancePrice, backpackPrice, lighterPrice }: SpreadMonitorManagerProps) {
  const [rules, setRules] = useState<SpreadMonitorRule[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const rulesRef = useRef<SpreadMonitorRule[]>([]);
  const [ruleTimers, setRuleTimers] = useState<Record<string, number>>({});
  const [newRule, setNewRule] = useState<Partial<SpreadMonitorRule>>({
    platformA: 'binance',
    platformB: 'lighter',
    operator: '>=',
    threshold: 100,
    enabled: true,
    notificationInterval: 5, // 默认5分钟
    maxNotifications: 10, // 默认最多10次
    notificationCount: 0,
    durationSeconds: 0 // 默认0秒，表示立即触发
  });

  // 从本地存储加载规则
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedRules = localStorage.getItem('spread_monitor_rules');
      if (savedRules) {
        try {
          const parsedRules = JSON.parse(savedRules);
          // 确保所有规则都有新字段的默认值
          const migratedRules = parsedRules.map((rule: any) => ({
            ...rule,
            notificationInterval: rule.notificationInterval || 5,
            maxNotifications: rule.maxNotifications || 10,
            notificationCount: rule.notificationCount || 0,
            durationSeconds: rule.durationSeconds || 0
          }));
          setRules(migratedRules);
        } catch (error) {
          console.error('Failed to load rules:', error);
        }
      }
    }
  }, []);

  // 更新 rulesRef 当 rules 改变时
  useEffect(() => {
    rulesRef.current = rules;
  }, [rules]);

  // 定时器更新计时器状态
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      const newTimers: Record<string, number> = {};

      rulesRef.current.forEach(rule => {
        if (rule.enabled &&
            rule.conditionStartTime &&
            (rule.durationSeconds || 0) > 0 &&
            (rule.notificationCount || 0) < (rule.maxNotifications || 10)) {
          const startTime = new Date(rule.conditionStartTime).getTime();
          const elapsedSeconds = Math.floor((now - startTime) / 1000);
          newTimers[rule.id] = elapsedSeconds;
        }
      });

      setRuleTimers(newTimers);
    }, 1000); // 每秒更新一次

    return () => clearInterval(interval);
  }, []);

  // 保存规则到本地存储
  const saveRules = (updatedRules: SpreadMonitorRule[]) => {
    setRules(updatedRules);
    if (typeof window !== 'undefined') {
      localStorage.setItem('spread_monitor_rules', JSON.stringify(updatedRules));
    }
  };

  // 添加新规则
  const handleAddRule = () => {
    if (newRule.platformA === newRule.platformB) {
      toast.error('请选择不同的交易所');
      return;
    }
    if (newRule.threshold === undefined || newRule.threshold === null || isNaN(newRule.threshold)) {
      toast.error('请输入有效的阈值');
      return;
    }
    if (!newRule.notificationInterval || newRule.notificationInterval <= 0) {
      toast.error('提醒间隔必须大于0');
      return;
    }
    if (!newRule.maxNotifications || newRule.maxNotifications <= 0) {
      toast.error('最大提醒次数必须大于0');
      return;
    }

    const rule: SpreadMonitorRule = {
      id: Date.now().toString(),
      platformA: newRule.platformA!,
      platformB: newRule.platformB!,
      operator: newRule.operator!,
      threshold: newRule.threshold,
      enabled: newRule.enabled!,
      createdAt: new Date().toISOString(),
      notificationInterval: newRule.notificationInterval!,
      maxNotifications: newRule.maxNotifications!,
      notificationCount: 0,
      durationSeconds: newRule.durationSeconds || 0
    };

    saveRules([...rules, rule]);
    setNewRule({
      platformA: 'binance',
      platformB: 'lighter',
      operator: '>=',
      threshold: 100,
      enabled: true,
      notificationInterval: 5,
      maxNotifications: 10,
      notificationCount: 0,
      durationSeconds: 0
    });
    setShowAddForm(false);
    toast.success('规则添加成功');
  };

  // 更新规则
  const handleUpdateRule = (updatedRule: SpreadMonitorRule) => {
    const updatedRules = rules.map(rule => 
      rule.id === updatedRule.id ? updatedRule : rule
    );
    saveRules(updatedRules);
    toast.success('规则更新成功');
  };

  // 删除规则
  const handleDeleteRule = (id: string) => {
    if (confirm('确定要删除这个监控规则吗？')) {
      const updatedRules = rules.filter(rule => rule.id !== id);
      saveRules(updatedRules);
      toast.success('规则删除成功');
    }
  };

  // 切换规则启用状态
  const handleToggleRule = (id: string) => {
    const updatedRules = rules.map(rule =>
      rule.id === id ? { ...rule, enabled: !rule.enabled } : rule
    );
    saveRules(updatedRules);
    const rule = updatedRules.find(r => r.id === id);
    toast.success(`规则已${rule?.enabled ? '启用' : '停用'}`);
  };

  // 重置提醒次数
  const handleResetNotificationCount = (id: string) => {
    const updatedRules = rules.map(rule =>
      rule.id === id ? { ...rule, notificationCount: 0 } : rule
    );
    saveRules(updatedRules);
    toast.success('提醒次数已重置');
  };

  // 检查价差并触发通知
  useEffect(() => {
    if (!binancePrice || !backpackPrice || !lighterPrice) {
      console.log('Price data not ready:', { binancePrice, backpackPrice, lighterPrice });
      return;
    }

    const prices = {
      binance: binancePrice,
      backpack: backpackPrice,
      lighter: lighterPrice
    };

    console.log('Current prices:', prices);

    rulesRef.current.forEach(rule => {
      if (!rule.enabled) {
        console.log(`Rule ${rule.id} is disabled, skipping`);
        return;
      }

      const priceA = prices[rule.platformA];
      const priceB = prices[rule.platformB];
      const spread = priceA - priceB;

      console.log(`🔍 Checking rule ${rule.id}:`, {
        platformA: rule.platformA,
        platformB: rule.platformB,
        priceA: priceA.toFixed(2),
        priceB: priceB.toFixed(2),
        spread: spread.toFixed(2),
        operator: rule.operator,
        threshold: rule.threshold,
        durationSeconds: rule.durationSeconds,
        hasConditionStartTime: !!rule.conditionStartTime,
        conditionStartTime: rule.conditionStartTime
      });

      let conditionMet = false;
      switch (rule.operator) {
        case '>=':
          conditionMet = spread >= rule.threshold;
          break;
        case '<=':
          conditionMet = spread <= rule.threshold;
          break;
        case '=':
          conditionMet = Math.abs(spread - rule.threshold) < 1; // 允许1美元误差
          break;
      }

      console.log(`📊 Condition result: ${conditionMet ? '✅ MET' : '❌ NOT MET'} for rule ${rule.id} (${spread.toFixed(2)} ${rule.operator} ${rule.threshold})`);

      const now = Date.now();
      const durationSeconds = rule.durationSeconds || 0;

      console.log(`🔧 DEBUG: Rule processing`, {
        ruleId: rule.id,
        conditionMet: conditionMet,
        durationSeconds: durationSeconds,
        hasConditionStartTime: !!rule.conditionStartTime,
        conditionStartTime: rule.conditionStartTime,
        isImmediateMode: durationSeconds === 0
      });

      if (conditionMet) {
        // 条件满足时的处理
        if (durationSeconds === 0) {
          // 立即触发模式（原有逻辑）
          console.log(`🚨 IMMEDIATE MODE TRIGGERED! Rule ${rule.id}`, {
            durationSeconds: durationSeconds,
            originalDurationSeconds: rule.durationSeconds,
            ruleData: rule
          });
          const lastTriggered = rule.lastTriggered ? new Date(rule.lastTriggered).getTime() : 0;
          const timeSinceLastTrigger = now - lastTriggered;
          const intervalMinutes = rule.notificationInterval || 5;
          const intervalMs = intervalMinutes * 60 * 1000;

          const currentCount = rule.notificationCount || 0;
          const maxCount = rule.maxNotifications || 10;

          console.log(`Rule triggered immediately: ${rule.platformA}-${rule.platformB} ${rule.operator} ${rule.threshold}`, {
            spread: spread.toFixed(2),
            priceA: priceA.toFixed(2),
            priceB: priceB.toFixed(2),
            timeSinceLastTrigger: Math.round(timeSinceLastTrigger / 1000) + 's',
            notificationCount: currentCount,
            maxNotifications: maxCount,
            intervalMinutes: intervalMinutes
          });

          // 检查是否超过最大提醒次数
          if (currentCount >= maxCount) {
            console.log('Skipping notification - max notifications reached');
            return;
          }

          // 检查是否在间隔时间内
          if (timeSinceLastTrigger < intervalMs) {
            console.log(`Skipping notification - too soon since last trigger (${intervalMinutes}min interval)`);
            return;
          }

          // 更新最后触发时间和提醒次数
          const updatedRules = rulesRef.current.map(r =>
            r.id === rule.id ? {
              ...r,
              lastTriggered: new Date().toISOString(),
              notificationCount: (r.notificationCount || 0) + 1
            } : r
          );
          saveRules(updatedRules);

          // 发送通知
          sendNotification(rule, spread, priceA, priceB);
        } else {
          // 持续时间模式
          if (!rule.conditionStartTime) {
            // 第一次满足条件，记录开始时间
            const startTime = new Date().toISOString();
            console.log(`⏱️ Timer started for rule: ${rule.platformA}-${rule.platformB} ${rule.operator} ${rule.threshold}`, {
              spread: spread.toFixed(2),
              durationSeconds: durationSeconds,
              startTime: startTime,
              message: `需要持续 ${durationSeconds} 秒才会触发通知`
            });

            const updatedRules = rulesRef.current.map(r =>
              r.id === rule.id ? {
                ...r,
                conditionStartTime: startTime
              } : r
            );
            saveRules(updatedRules);
            // 立即更新 rulesRef 以确保下次检查时使用最新数据
            rulesRef.current = updatedRules;
          } else {
            // 检查是否已持续足够时间
            const conditionStartTime = new Date(rule.conditionStartTime).getTime();
            const elapsedMs = now - conditionStartTime;
            const elapsedSeconds = Math.floor(elapsedMs / 1000);

            console.log(`⏰ Duration check: ${rule.platformA}-${rule.platformB}`, {
              spread: spread.toFixed(2),
              elapsedSeconds: elapsedSeconds,
              elapsedMs: elapsedMs,
              requiredSeconds: durationSeconds,
              conditionStartTime: rule.conditionStartTime,
              currentTime: new Date(now).toISOString(),
              willTrigger: elapsedSeconds >= durationSeconds
            });

            // 添加额外的安全检查：确保真的持续了足够的时间
            // 要求至少持续 durationSeconds + 0.5秒 以避免边界情况
            const requiredMs = durationSeconds * 1000 + 500; // 额外0.5秒缓冲
            const actuallyReachedDuration = elapsedMs >= requiredMs;

            if (elapsedSeconds >= durationSeconds && actuallyReachedDuration) {
              // 持续时间达到，检查通知限制
              const lastTriggered = rule.lastTriggered ? new Date(rule.lastTriggered).getTime() : 0;
              const timeSinceLastTrigger = now - lastTriggered;
              const intervalMinutes = rule.notificationInterval || 5;
              const intervalMs = intervalMinutes * 60 * 1000;

              const currentCount = rule.notificationCount || 0;
              const maxCount = rule.maxNotifications || 10;

              console.log(`🚨 DURATION REACHED! ${rule.platformA}-${rule.platformB}`, {
                spread: spread.toFixed(2),
                elapsedSeconds: elapsedSeconds,
                elapsedMs: elapsedMs,
                requiredSeconds: durationSeconds,
                requiredMs: requiredMs,
                actuallyReachedDuration: actuallyReachedDuration,
                timeSinceLastTrigger: Math.round(timeSinceLastTrigger / 1000) + 's',
                notificationCount: currentCount,
                maxNotifications: maxCount,
                conditionStartTime: rule.conditionStartTime,
                currentTime: new Date(now).toISOString()
              });

              // 检查是否超过最大提醒次数
              if (currentCount >= maxCount) {
                console.log('Skipping notification - max notifications reached');
                // 重置条件开始时间，避免计时器继续显示
                if (rule.conditionStartTime) {
                  const updatedRules = rulesRef.current.map(r =>
                    r.id === rule.id ? {
                      ...r,
                      conditionStartTime: undefined
                    } : r
                  );
                  saveRules(updatedRules);
                  rulesRef.current = updatedRules;
                }
                return;
              }

              // 检查是否在间隔时间内
              if (timeSinceLastTrigger < intervalMs) {
                console.log(`Skipping notification - too soon since last trigger (${intervalMinutes}min interval)`);
                // 重置条件开始时间，因为已经触发过了，需要等待间隔时间
                const updatedRules = rulesRef.current.map(r =>
                  r.id === rule.id ? {
                    ...r,
                    conditionStartTime: undefined
                  } : r
                );
                saveRules(updatedRules);
                rulesRef.current = updatedRules;
                return;
              }

              // 更新最后触发时间、提醒次数，并重置条件开始时间
              const updatedRules = rulesRef.current.map(r =>
                r.id === rule.id ? {
                  ...r,
                  lastTriggered: new Date().toISOString(),
                  notificationCount: (r.notificationCount || 0) + 1,
                  conditionStartTime: undefined // 重置条件开始时间，等待间隔时间后重新开始
                } : r
              );
              saveRules(updatedRules);
              // 立即更新 rulesRef
              rulesRef.current = updatedRules;

              // 发送通知
              sendNotification(rule, spread, priceA, priceB);
            }
          }
        }
      } else {
        // 条件不满足时，重置条件开始时间和计时器
        if (rule.conditionStartTime) {
          console.log(`❌ Condition no longer met, resetting timer for rule: ${rule.platformA}-${rule.platformB}`, {
            spread: spread.toFixed(2),
            threshold: rule.threshold,
            operator: rule.operator
          });

          const updatedRules = rulesRef.current.map(r =>
            r.id === rule.id ? {
              ...r,
              conditionStartTime: undefined
            } : r
          );
          saveRules(updatedRules);
          // 立即更新 rulesRef
          rulesRef.current = updatedRules;

          // 立即清除计时器显示
          setRuleTimers(prev => {
            const newTimers = { ...prev };
            delete newTimers[rule.id];
            return newTimers;
          });
        }
      }
    });
  }, [binancePrice, backpackPrice, lighterPrice]);

  // 发送 Bark 通知
  const sendNotification = async (rule: SpreadMonitorRule, spread: number, priceA: number, priceB: number) => {
    const barkApiKey = localStorage.getItem('bark_api_key');
    if (!barkApiKey) {
      console.log('No Bark API key found, notification not sent');
      return;
    }

    console.log('Sending notification for rule:', rule.id, 'spread:', spread.toFixed(2));

    const platformAName = PLATFORMS.find(p => p.id === rule.platformA)?.name;
    const platformBName = PLATFORMS.find(p => p.id === rule.platformB)?.name;

    const title = `${platformAName}-${platformBName}-BTC价差 $${spread.toFixed(2)}`;
    const message = `价差监控触发`;

    try {
      // 使用正确的 Bark API 格式：URL 中包含标题和内容
      const url = `https://api.day.app/${barkApiKey}/${encodeURIComponent(title)}/${encodeURIComponent(message)}?level=critical&volume=10`;

      console.log('Sending Bark notification:', {
        title,
        message,
        spread: spread.toFixed(2),
        url: url
      });

      const response = await fetch(url, {
        method: 'POST'
      });

      const responseText = await response.text();
      console.log('Bark response:', {
        status: response.status,
        statusText: response.statusText,
        body: responseText
      });

      if (response.ok) {
        console.log('✅ Notification sent successfully');
      } else {
        console.error('❌ Notification failed:', response.status, responseText);
      }
    } catch (error) {
      console.error('❌ Failed to send notification:', error);
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-4 w-4 text-orange-500" />
          <span className="font-medium text-sm">价差监控规则</span>
          <span className="text-xs text-gray-500">({rules.filter(r => r.enabled).length}/{rules.length} 启用)</span>
        </div>
        <Button
          onClick={() => setShowAddForm(true)}
          size="sm"
          className="h-7 px-2 text-xs"
        >
          <Plus className="h-3 w-3 mr-1" />
          新增规则
        </Button>
      </div>

      {/* 添加规则表单 */}
      {showAddForm && (
        <div className="bg-blue-50 rounded-lg p-4 border-2 border-blue-200">
          <div className="grid grid-cols-1 md:grid-cols-7 gap-3 items-end">
            <div>
              <label className="block text-xs text-gray-600 mb-1">平台A</label>
              <select
                value={newRule.platformA}
                onChange={(e) => setNewRule({...newRule, platformA: e.target.value as any})}
                className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {PLATFORMS.map(platform => (
                  <option key={platform.id} value={platform.id}>
                    {platform.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-xs text-gray-600 mb-1">平台B</label>
              <select
                value={newRule.platformB}
                onChange={(e) => setNewRule({...newRule, platformB: e.target.value as any})}
                className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {PLATFORMS.map(platform => (
                  <option key={platform.id} value={platform.id}>
                    {platform.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-xs text-gray-600 mb-1">条件</label>
              <select
                value={newRule.operator}
                onChange={(e) => setNewRule({...newRule, operator: e.target.value as any})}
                className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {OPERATORS.map(op => (
                  <option key={op.value} value={op.value}>
                    {op.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-xs text-gray-600 mb-1">阈值($)</label>
              <input
                type="number"
                value={newRule.threshold}
                onChange={(e) => setNewRule({...newRule, threshold: parseFloat(e.target.value) || 0})}
                className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="100"
                step="0.01"
              />
            </div>

            <div>
              <label className="block text-xs text-gray-600 mb-1">间隔(分钟)</label>
              <input
                type="number"
                value={newRule.notificationInterval}
                onChange={(e) => setNewRule({...newRule, notificationInterval: parseInt(e.target.value) || 5})}
                className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="5"
                min="1"
                step="1"
              />
            </div>

            <div>
              <label className="block text-xs text-gray-600 mb-1">最大次数</label>
              <input
                type="number"
                value={newRule.maxNotifications}
                onChange={(e) => setNewRule({...newRule, maxNotifications: parseInt(e.target.value) || 10})}
                className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="10"
                min="1"
                step="1"
              />
            </div>

            <div className="flex gap-1">
              <Button
                onClick={handleAddRule}
                size="sm"
                className="h-7 px-2 text-xs bg-green-600 hover:bg-green-700"
              >
                保存
              </Button>
              <Button
                onClick={() => setShowAddForm(false)}
                variant="outline"
                size="sm"
                className="h-7 px-2 text-xs"
              >
                取消
              </Button>
            </div>
          </div>

          {/* 持续时间设置 - 单独一行 */}
          <div className="mt-3 pt-3 border-t border-blue-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 items-end">
              <div>
                <label className="block text-xs text-gray-600 mb-1">持续时间(秒)</label>
                <input
                  type="number"
                  value={newRule.durationSeconds || 0}
                  onChange={(e) => setNewRule({...newRule, durationSeconds: parseInt(e.target.value) || 0})}
                  className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0"
                  min="0"
                  step="1"
                />
              </div>
              <div className="text-xs text-gray-500 md:col-span-2">
                设置为0表示立即触发；大于0表示条件满足并持续指定秒数后才触发提醒
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 规则列表 */}
      <div className="space-y-2">
        {rules.length === 0 ? (
          <div className="text-center py-8 text-gray-500 text-sm">
            暂无监控规则，点击"新增规则"开始添加
          </div>
        ) : (
          rules.map(rule => (
            <SpreadMonitorRuleComponent
              key={rule.id}
              rule={rule}
              onUpdate={handleUpdateRule}
              onDelete={handleDeleteRule}
              onToggle={handleToggleRule}
              onResetCount={handleResetNotificationCount}
              currentTimer={ruleTimers[rule.id]}
            />
          ))
        )}
      </div>
    </div>
  );
}
