'use client';

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PriceData } from '@/types/price';
import { TrendingUp, TrendingDown, Wifi, WifiOff } from 'lucide-react';

interface PriceCardProps {
  priceData: PriceData | null;
  loading?: boolean;
}

export function PriceCard({ priceData, loading = false }: PriceCardProps) {
  const getExchangeDisplayName = (exchange: string) => {
    switch (exchange) {
      case 'binance':
        return '币安 Binance';
      case 'backpack':
        return 'Backpack';
      case 'lighter':
        return 'Lighter';
      default:
        return exchange;
    }
  };

  const getExchangeColor = (exchange: string) => {
    switch (exchange) {
      case 'binance':
        return 'bg-yellow-500';
      case 'backpack':
        return 'bg-purple-500';
      case 'lighter':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="h-6 bg-gray-200 rounded animate-pulse w-32"></div>
            <div className="h-5 bg-gray-200 rounded animate-pulse w-16"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="h-8 bg-gray-200 rounded animate-pulse w-40"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!priceData) {
    return (
      <Card className="w-full border-red-200">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold text-gray-600">
              交易所数据
            </CardTitle>
            <Badge variant="destructive" className="flex items-center gap-1">
              <WifiOff className="h-3 w-3" />
              离线
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-gray-500">暂无数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const isPositiveChange = (priceData.changePercent24h || 0) >= 0;

  return (
    <Card className="w-full hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <div 
              className={`w-3 h-3 rounded-full ${getExchangeColor(priceData.exchange)}`}
            ></div>
            {getExchangeDisplayName(priceData.exchange)}
          </CardTitle>
          <Badge 
            variant={priceData.connected ? "default" : "destructive"}
            className="flex items-center gap-1"
          >
            {priceData.connected ? (
              <>
                <Wifi className="h-3 w-3" />
                在线
              </>
            ) : (
              <>
                <WifiOff className="h-3 w-3" />
                离线
              </>
            )}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {formatPrice(priceData.price)}
              </p>
              <p className="text-sm text-gray-500">{priceData.symbol}</p>
            </div>
            {priceData.changePercent24h !== undefined && (
              <div className={`flex items-center gap-1 px-2 py-1 rounded-md ${
                isPositiveChange 
                  ? 'bg-green-100 text-green-700' 
                  : 'bg-red-100 text-red-700'
              }`}>
                {isPositiveChange ? (
                  <TrendingUp className="h-4 w-4" />
                ) : (
                  <TrendingDown className="h-4 w-4" />
                )}
                <span className="text-sm font-medium">
                  {isPositiveChange ? '+' : ''}{priceData.changePercent24h.toFixed(2)}%
                </span>
              </div>
            )}
          </div>
          <div className="text-xs text-gray-400">
            更新时间: {formatTime(priceData.timestamp)}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
