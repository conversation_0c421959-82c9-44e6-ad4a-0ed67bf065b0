'use client';

import { Card, CardContent } from '@/components/ui/card';
import { PriceData } from '@/types/price';

interface SimplePriceCardProps {
  priceData: PriceData | null;
  loading?: boolean;
}

export function SimplePriceCard({ priceData, loading = false }: SimplePriceCardProps) {
  const getExchangeDisplayName = (exchange: string) => {
    switch (exchange) {
      case 'binance':
        return '币安';
      case 'backpack':
        return 'Backpack';
      case 'lighter':
        return 'Lighter';
      default:
        return exchange;
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price);
  };

  if (loading) {
    return (
      <Card className="w-full h-20">
        <CardContent className="p-3 h-full flex items-center justify-center">
          <div className="text-center space-y-1">
            <div className="h-3 bg-gray-200 rounded animate-pulse w-16 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-20 mx-auto"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!priceData) {
    return (
      <Card className="w-full h-20 border-gray-300">
        <CardContent className="p-3 h-full flex items-center justify-center">
          <div className="text-center">
            <div className="text-gray-400 text-xs mb-1">交易所</div>
            <div className="text-gray-500 text-sm">暂无数据</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full h-20 hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-3 h-full flex flex-col justify-center">
        <div className="text-center space-y-1">
          {/* 交易所名称 */}
          <div className="flex items-center justify-center gap-1">
            <div className={`w-1.5 h-1.5 rounded-full ${priceData.connected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-xs font-medium text-gray-700">
              {getExchangeDisplayName(priceData.exchange)}
            </span>
          </div>

          {/* 价格 */}
          <div className="text-base font-bold text-gray-900">
            {formatPrice(priceData.price)}
          </div>

          {/* 交易对 */}
          <div className="text-xs text-gray-500">
            {priceData.symbol}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
