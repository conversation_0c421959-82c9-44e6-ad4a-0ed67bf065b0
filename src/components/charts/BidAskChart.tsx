import { useMemo } from 'react';
import {
  ComposedChart,
  Bar,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { NewHistoryRecord } from '@/types/history';

interface ChartDataPoint {
  timestamp: string;
  time: string;
  lighterBid: number;
  lighterAsk: number;
  lighterMid: number;
  lighterSpread: number;
  spreadHeight: number;
}

interface BidAskChartProps {
  data: NewHistoryRecord[];
  height?: number;
}

export function BidAskChart({ data, height = 500 }: BidAskChartProps) {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    const processedData = data.map((record, index) => ({
      id: `${record.timestamp}-${index}`, // 添加稳定的ID
      timestamp: record.timestamp,
      time: record.timestamp.slice(5, 16).replace(' ', '\n'), // 简化时间格式
      lighterBid: Number(record.lighter_bid.toFixed(1)),
      lighterAsk: Number(record.lighter_ask.toFixed(1)),
      lighterMid: Number(record.lighter_mid.toFixed(1)),
      lighterSpread: Number(record.lighter_spread.toFixed(2)),
      spreadHeight: Number((record.lighter_ask - record.lighter_bid).toFixed(2)),
    }));

    // 反转数组，让时间从左到右递增（最新时间在右边）
    const reversedData = [...processedData].reverse();

    // 如果数据量太大，进行稳定采样
    if (reversedData.length > 1000) {
      const step = Math.ceil(reversedData.length / 1000);
      const sampled = [];
      for (let i = 0; i < reversedData.length; i += step) {
        sampled.push(reversedData[i]);
      }
      return sampled;
    }

    return reversedData;
  }, [data.length, data[0]?.timestamp]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          <div className="space-y-1 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded"></div>
              <span>卖价(Ask): <span className="font-medium">${data.lighterAsk.toFixed(1)}</span></span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span>中间价: <span className="font-medium">${data.lighterMid.toFixed(1)}</span></span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span>买价(Bid): <span className="font-medium">${data.lighterBid.toFixed(1)}</span></span>
            </div>
            <div className="border-t border-gray-100 pt-2 mt-2">
              <div className="text-xs text-gray-600">
                <div>买卖价差: ${data.lighterSpread.toFixed(2)}</div>
                <div>价差幅度: ${data.spreadHeight.toFixed(2)}</div>
              </div>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  // 计算Y轴范围
  const yAxisDomain = useMemo(() => {
    if (chartData.length === 0) return [109000, 110000];
    
    const allPrices = chartData.flatMap(d => [d.lighterBid, d.lighterAsk]);
    const min = Math.min(...allPrices);
    const max = Math.max(...allPrices);
    const padding = (max - min) * 0.05; // 5% padding
    
    return [min - padding, max + padding];
  }, [chartData]);

  if (chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        暂无买卖价差数据
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Lighter 买卖价差图</h3>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>买价(Bid)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span>中间价</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span>卖价(Ask)</span>
          </div>
          <div className="text-xs text-gray-500">
            数据点: {chartData.length} / {data.length}
          </div>
        </div>
        <div className="mt-2 p-3 bg-green-50 rounded-lg">
          <p className="text-sm text-green-800">
            💡 <strong>买卖价差解读：</strong>
            买卖价差反映市场流动性，价差越小流动性越好。
            中间价是买价和卖价的平均值。
          </p>
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height={height}>
        <ComposedChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="time"
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={60}
            interval="preserveStartEnd"
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            domain={yAxisDomain}
            tickFormatter={(value) => `$${value.toLocaleString()}`}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          {/* 买价线 */}
          <Line
            type="monotone"
            dataKey="lighterBid"
            stroke="#10b981"
            strokeWidth={2}
            dot={false}
            name="买价(Bid)"
            connectNulls={false}
          />
          
          {/* 中间价线 */}
          <Line
            type="monotone"
            dataKey="lighterMid"
            stroke="#3b82f6"
            strokeWidth={2}
            dot={false}
            name="中间价"
            connectNulls={false}
          />
          
          {/* 卖价线 */}
          <Line
            type="monotone"
            dataKey="lighterAsk"
            stroke="#ef4444"
            strokeWidth={2}
            dot={false}
            name="卖价(Ask)"
            connectNulls={false}
          />
        </ComposedChart>
      </ResponsiveContainer>
      
      {/* 价差统计 */}
      <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm text-gray-600">平均价差</div>
          <div className="text-lg font-semibold">
            ${(chartData.reduce((sum, d) => sum + d.lighterSpread, 0) / chartData.length).toFixed(2)}
          </div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm text-gray-600">最大价差</div>
          <div className="text-lg font-semibold">
            ${Math.max(...chartData.map(d => d.lighterSpread)).toFixed(2)}
          </div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm text-gray-600">最小价差</div>
          <div className="text-lg font-semibold">
            ${Math.min(...chartData.map(d => d.lighterSpread)).toFixed(2)}
          </div>
        </div>
      </div>
    </div>
  );
}
