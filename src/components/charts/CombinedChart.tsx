import { useMemo } from 'react';
import {
  ComposedChart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import { NewHistoryRecord } from '@/types/history';

interface ChartDataPoint {
  timestamp: string;
  time: string;
  binancePrice: number;
  backpackPrice: number;
  lighterMid: number;
  binanceSpread: number;
  backpackSpread: number;
  lighterSpread: number;
}

interface CombinedChartProps {
  data: NewHistoryRecord[];
  height?: number;
}

export function CombinedChart({ data, height = 600 }: CombinedChartProps) {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    const processedData = data.map((record, index) => {
      const binanceSpread = record.binance_price - record.lighter_mid;
      const backpackSpread = record.backpack_price - record.lighter_mid;

      return {
        id: `${record.timestamp}-${index}`, // 添加稳定的ID
        timestamp: record.timestamp,
        time: record.timestamp.slice(5, 16).replace(' ', '\n'), // 简化时间格式
        binancePrice: Number(record.binance_price.toFixed(1)),
        backpackPrice: Number(record.backpack_price.toFixed(1)),
        lighterMid: Number(record.lighter_mid.toFixed(1)),
        binanceSpread: Number(binanceSpread.toFixed(2)),
        backpackSpread: Number(backpackSpread.toFixed(2)),
        lighterSpread: Number(record.lighter_spread.toFixed(2)),
      };
    });

    // 反转数组，让时间从左到右递增（最新时间在右边）
    const reversedData = [...processedData].reverse();

    // 如果数据量太大，进行稳定采样
    if (reversedData.length > 1000) {
      const step = Math.ceil(reversedData.length / 1000);
      const sampled = [];
      for (let i = 0; i < reversedData.length; i += step) {
        sampled.push(reversedData[i]);
      }
      return sampled;
    }

    return reversedData;
  }, [data.length, data[0]?.timestamp]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg max-w-xs">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          
          <div className="space-y-2 text-sm">
            <div className="border-b border-gray-100 pb-2">
              <div className="font-medium text-gray-700 mb-1">价格</div>
              <div className="space-y-1">
                <div>币安: ${data.binancePrice.toFixed(1)}</div>
                <div>Backpack: ${data.backpackPrice.toFixed(1)}</div>
                <div>Lighter: ${data.lighterMid.toFixed(1)}</div>
              </div>
            </div>
            
            <div className="border-b border-gray-100 pb-2">
              <div className="font-medium text-gray-700 mb-1">价差 (vs Lighter)</div>
              <div className="space-y-1">
                <div className={data.binanceSpread >= 0 ? 'text-green-600' : 'text-red-600'}>
                  币安: ${data.binanceSpread}
                </div>
                <div className={data.backpackSpread >= 0 ? 'text-green-600' : 'text-red-600'}>
                  Backpack: ${data.backpackSpread}
                </div>
              </div>
            </div>
            
            <div>
              <div className="font-medium text-gray-700 mb-1">Lighter买卖价差</div>
              <div>${data.lighterSpread.toFixed(2)}</div>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  if (chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        暂无组合图表数据
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      {/* 价格 + 价差组合图 */}
      <div>
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">价格与价差组合分析</h3>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span>币安价格</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span>Backpack价格</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-orange-500 rounded"></div>
              <span>Lighter价格</span>
            </div>
            <div className="text-xs text-gray-500">
              数据点: {chartData.length} / {data.length}
            </div>
          </div>
        </div>
        
        <ResponsiveContainer width="100%" height={height * 0.6}>
          <ComposedChart
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 60,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="time"
              tick={{ fontSize: 12 }}
              angle={-45}
              textAnchor="end"
              height={60}
              interval="preserveStartEnd"
            />
            <YAxis 
              yAxisId="price"
              orientation="left"
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => `$${value.toLocaleString()}`}
            />
            <YAxis 
              yAxisId="spread"
              orientation="right"
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => `$${value}`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            
            {/* 价格线 */}
            <Line
              yAxisId="price"
              type="monotone"
              dataKey="binancePrice"
              stroke="#3b82f6"
              strokeWidth={2}
              dot={false}
              name="币安价格"
              connectNulls={false}
            />
            <Line
              yAxisId="price"
              type="monotone"
              dataKey="backpackPrice"
              stroke="#10b981"
              strokeWidth={2}
              dot={false}
              name="Backpack价格"
              connectNulls={false}
            />
            <Line
              yAxisId="price"
              type="monotone"
              dataKey="lighterMid"
              stroke="#f59e0b"
              strokeWidth={2}
              dot={false}
              name="Lighter价格"
              connectNulls={false}
            />
            
            {/* 价差柱状图 */}
            <Bar
              yAxisId="spread"
              dataKey="binanceSpread"
              fill="#3b82f6"
              fillOpacity={0.3}
              name="币安价差"
            />
            <Bar
              yAxisId="spread"
              dataKey="backpackSpread"
              fill="#10b981"
              fillOpacity={0.3}
              name="Backpack价差"
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>

      {/* 价差详细分析 */}
      <div>
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">价差详细分析</h3>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span>币安-Lighter价差</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span>Backpack-Lighter价差</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-purple-500 rounded"></div>
              <span>Lighter买卖价差</span>
            </div>
          </div>
        </div>
        
        <ResponsiveContainer width="100%" height={height * 0.4}>
          <ComposedChart
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 60,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="time"
              tick={{ fontSize: 12 }}
              angle={-45}
              textAnchor="end"
              height={60}
              interval="preserveStartEnd"
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => `$${value}`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            
            {/* 零线参考 */}
            <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
            
            {/* 价差线 */}
            <Line
              type="monotone"
              dataKey="binanceSpread"
              stroke="#3b82f6"
              strokeWidth={2}
              dot={false}
              name="币安-Lighter价差"
              connectNulls={false}
            />
            <Line
              type="monotone"
              dataKey="backpackSpread"
              stroke="#10b981"
              strokeWidth={2}
              dot={false}
              name="Backpack-Lighter价差"
              connectNulls={false}
            />
            <Line
              type="monotone"
              dataKey="lighterSpread"
              stroke="#8b5cf6"
              strokeWidth={2}
              dot={false}
              name="Lighter买卖价差"
              connectNulls={false}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
