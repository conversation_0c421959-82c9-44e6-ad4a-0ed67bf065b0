import { useMemo, memo, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import { NewHistoryRecord } from '@/types/history';


interface ChartDataPoint {
  timestamp: string;
  time: string;
  spread: number;
  spreadName: string;
}

type Platform = 'binance' | 'backpack' | 'lighter';

interface PlatformOption {
  value: Platform;
  label: string;
  color: string;
}

interface SpreadAnalysisChartProps {
  data: NewHistoryRecord[];
  height?: number;
}

export const SpreadAnalysisChart = memo(function SpreadAnalysisChart({ data, height = 500 }: SpreadAnalysisChartProps) {
  const [platform1, setPlatform1] = useState<Platform>('binance');
  const [platform2, setPlatform2] = useState<Platform>('lighter');

  const platformOptions: PlatformOption[] = [
    { value: 'binance', label: '币安', color: '#f59e0b' },
    { value: 'backpack', label: 'Backpack', color: '#10b981' },
    { value: 'lighter', label: 'Lighter', color: '#6366f1' },
  ];

  const getPlatformPrice = (record: NewHistoryRecord, platform: Platform): number => {
    switch (platform) {
      case 'binance':
        return record.binance_price;
      case 'backpack':
        return record.backpack_price;
      case 'lighter':
        return record.lighter_mid;
      default:
        return 0;
    }
  };
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    const processedData = data.map((record, index) => {
      const price1 = getPlatformPrice(record, platform1);
      const price2 = getPlatformPrice(record, platform2);

      // 计算平台A相对于平台B的价差
      const spread = price1 - price2;

      const platform1Label = platformOptions.find(p => p.value === platform1)?.label || platform1;
      const platform2Label = platformOptions.find(p => p.value === platform2)?.label || platform2;

      return {
        id: `${record.timestamp}-${index}`, // 添加稳定的ID
        timestamp: record.timestamp,
        time: record.timestamp.slice(5, 16).replace(' ', '\n'), // 简化时间格式
        spread: Number(spread.toFixed(2)),
        spreadName: `${platform1Label}-${platform2Label}`,
      };
    });

    // 反转数组，让时间从左到右递增（最新时间在右边）
    const reversedData = [...processedData].reverse();

    // 如果数据量太大，进行稳定采样
    if (reversedData.length > 1000) {
      const step = Math.ceil(reversedData.length / 1000);
      const sampled = [];
      for (let i = 0; i < reversedData.length; i += step) {
        sampled.push(reversedData[i]);
      }
      return sampled;
    }

    return reversedData;
  }, [data.length, data[0]?.timestamp, platform1, platform2]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          <div className="space-y-1 text-sm">
            {payload.map((entry: any, index: number) => (
              <div key={index} className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded" 
                  style={{ backgroundColor: entry.color }}
                ></div>
                <span>{entry.name}: <span className="font-medium">${entry.value}</span></span>
              </div>
            ))}
          </div>
          <div className="border-t border-gray-100 pt-2 mt-2 text-xs text-gray-600">
            <p>正值表示平台A价格高于平台B，负值表示平台A价格低于平台B</p>
          </div>
        </div>
      );
    }
    return null;
  };

  // 计算Y轴范围
  const yAxisDomain = useMemo(() => {
    if (chartData.length === 0) return [-100, 100];

    const allSpreads = chartData.map(d => d.spread);
    const min = Math.min(...allSpreads);
    const max = Math.max(...allSpreads);
    const padding = Math.abs(max - min) * 0.1; // 10% padding

    return [min - padding, max + padding];
  }, [chartData]);

  if (chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        暂无价差分析数据
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">价差分析图</h3>

        {/* 平台选择器 */}
        <div className="mb-3 p-2 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <label className="text-xs font-medium text-gray-700">平台A</label>
              <select
                value={platform1}
                onChange={(e) => setPlatform1(e.target.value as Platform)}
                className="h-8 px-2 py-1 border border-gray-300 rounded bg-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                {platformOptions.map((option) => (
                  <option
                    key={option.value}
                    value={option.value}
                    disabled={option.value === platform2}
                  >
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <span className="text-xs text-gray-500">-</span>

            <div className="flex items-center gap-2">
              <label className="text-xs font-medium text-gray-700">平台B</label>
              <select
                value={platform2}
                onChange={(e) => setPlatform2(e.target.value as Platform)}
                className="h-8 px-2 py-1 border border-gray-300 rounded bg-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                {platformOptions.map((option) => (
                  <option
                    key={option.value}
                    value={option.value}
                    disabled={option.value === platform1}
                  >
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="text-xs text-gray-600 ml-2">
              价差：{platformOptions.find(p => p.value === platform1)?.label} - {platformOptions.find(p => p.value === platform2)?.label}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span>{chartData[0]?.spreadName || '价差'}</span>
          </div>
          <div className="text-xs text-gray-500">
            数据点: {chartData.length} / {data.length}
          </div>
        </div>
        <div className="mt-2 p-2 bg-blue-50 rounded">
          <p className="text-xs text-blue-800">
            💡 <strong>价差解读：</strong>
            正值表示平台A价格高于平台B，负值表示平台A价格低于平台B。
            价差越大，套利机会越明显。
          </p>
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height={height}>
        <LineChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="time"
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={60}
            interval="preserveStartEnd"
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            domain={yAxisDomain}
            tickFormatter={(value) => `$${value}`}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          {/* 零线参考 */}
          <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
          
          {/* 价差线 */}
          <Line
            type="monotone"
            dataKey="spread"
            stroke="#3b82f6"
            strokeWidth={2}
            dot={false}
            name={chartData[0]?.spreadName || '价差'}
            connectNulls={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
});
