import { useMemo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import { NewHistoryRecord } from '@/types/history';

interface ChartDataPoint {
  timestamp: string;
  time: string;
  binanceSpread: number;
  backpackSpread: number;
  binancePrice: number;
  backpackPrice: number;
  lighterPrice: number;
}

interface PriceSpreadChartProps {
  data: NewHistoryRecord[];
  height?: number;
}

export function PriceSpreadChart({ data, height = 400 }: PriceSpreadChartProps) {
  // 处理和优化数据
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // 对于大数据量，可以考虑采样
    const processedData = data.map((record) => {
      const binanceSpread = record.binance_price - record.lighter_mid;
      const backpackSpread = record.backpack_price - record.lighter_mid;

      return {
        timestamp: record.timestamp,
        time: new Date(record.timestamp).toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }),
        binanceSpread: Number(binanceSpread.toFixed(2)),
        backpackSpread: Number(backpackSpread.toFixed(2)),
        binancePrice: record.binance_price,
        backpackPrice: record.backpack_price,
        lighterPrice: record.lighter_mid,
      };
    });

    // 反转数组，让时间从左到右递增（最新时间在右边）
    const reversedData = [...processedData].reverse();

    // 调试：检查时间顺序
    if (reversedData.length > 0) {
      console.log('Spread chart data order:', {
        first: reversedData[0]?.timestamp,
        last: reversedData[reversedData.length - 1]?.timestamp,
        total: reversedData.length,
        message: 'Should be: oldest -> newest (left to right)'
      });
    }

    // 如果数据量太大（超过1000条），进行采样
    if (reversedData.length > 1000) {
      const step = Math.ceil(reversedData.length / 1000);
      const sampled = [];
      for (let i = 0; i < reversedData.length; i += step) {
        sampled.push(reversedData[i]);
      }
      console.log('Spread chart sampled:', {
        original: reversedData.length,
        sampled: sampled.length,
        step: step
      });
      return sampled;
    }

    return reversedData;
  }, [data]);

  // 自定义 Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          <div className="space-y-1 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span>币安价差: <span className="font-medium">${data.binanceSpread}</span></span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span>Backpack价差: <span className="font-medium">${data.backpackSpread}</span></span>
            </div>
            <div className="border-t border-gray-100 pt-2 mt-2">
              <div className="text-xs text-gray-600 space-y-1">
                <div>币安: ${data.binancePrice.toFixed(1)}</div>
                <div>Backpack: ${data.backpackPrice.toFixed(1)}</div>
                <div>Lighter: ${data.lighterPrice.toFixed(1)}</div>
              </div>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  // 计算Y轴范围
  const yAxisDomain = useMemo(() => {
    if (chartData.length === 0) return [-100, 100];
    
    const allSpreads = chartData.flatMap(d => [d.binanceSpread, d.backpackSpread]);
    const min = Math.min(...allSpreads);
    const max = Math.max(...allSpreads);
    const padding = Math.abs(max - min) * 0.1; // 10% padding
    
    return [min - padding, max + padding];
  }, [chartData]);

  if (chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        暂无图表数据
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">价差趋势图</h3>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span>币安-Lighter价差</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>Backpack-Lighter价差</span>
          </div>
          <div className="text-xs text-gray-500">
            数据点: {chartData.length} / {data.length}
          </div>
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height={height}>
        <LineChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="time"
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={60}
            interval="preserveStartEnd"
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            domain={yAxisDomain}
            tickFormatter={(value) => `$${value}`}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          {/* 零线参考 */}
          <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
          
          {/* 价差线 */}
          <Line
            type="monotone"
            dataKey="binanceSpread"
            stroke="#3b82f6"
            strokeWidth={2}
            dot={false}
            name="币安-Lighter"
            connectNulls={false}
          />
          <Line
            type="monotone"
            dataKey="backpackSpread"
            stroke="#10b981"
            strokeWidth={2}
            dot={false}
            name="Backpack-Lighter"
            connectNulls={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
