'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PriceData } from '@/types/price';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface SpreadDisplayProps {
  prices: {
    binance: PriceData | null;
    backpack: PriceData | null;
    lighter: PriceData | null;
  };
}

export function SpreadDisplay({ prices }: SpreadDisplayProps) {
  // 计算币安 vs Lighter 价差
  const calculateBinanceLighterSpread = () => {
    if (!prices.binance || !prices.lighter) return null;
    
    const spread = prices.binance.price - prices.lighter.price;
    const spreadPercent = ((spread / prices.lighter.price) * 100);
    
    return {
      absolute: spread,
      percentage: spreadPercent,
      isPositive: spread >= 0
    };
  };

  // 计算 Backpack vs Lighter 价差
  const calculateBackpackLighterSpread = () => {
    if (!prices.backpack || !prices.lighter) return null;
    
    const spread = prices.backpack.price - prices.lighter.price;
    const spreadPercent = ((spread / prices.lighter.price) * 100);
    
    return {
      absolute: spread,
      percentage: spreadPercent,
      isPositive: spread >= 0
    };
  };

  const binanceLighterSpread = calculateBinanceLighterSpread();
  const backpackLighterSpread = calculateBackpackLighterSpread();

  const formatSpread = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      signDisplay: 'always'
    }).format(value);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
      {/* 币安 vs Lighter 价差卡片 */}
      <div className="bg-gray-50 rounded p-3 text-sm">
        <div className="flex items-center justify-between mb-2">
          <span className="font-medium text-gray-700">币安-Lighter</span>
          {binanceLighterSpread && (
            binanceLighterSpread.isPositive ? (
              <TrendingUp className="h-3 w-3 text-green-500" />
            ) : (
              <TrendingDown className="h-3 w-3 text-red-500" />
            )
          )}
        </div>
        {binanceLighterSpread ? (
          <div className="space-y-1">
            <div className={`text-lg font-bold ${
              binanceLighterSpread.isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              {formatSpread(binanceLighterSpread.absolute)}
            </div>
            <div className="flex items-center gap-2">
              <span className={`text-xs px-1.5 py-0.5 rounded ${
                binanceLighterSpread.isPositive ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}>
                {binanceLighterSpread.isPositive ? '+' : ''}{binanceLighterSpread.percentage.toFixed(2)}%
              </span>
              <span className="text-xs text-gray-500">
                {binanceLighterSpread.isPositive ? '币安更高' : 'Lighter更高'}
              </span>
            </div>
          </div>
        ) : (
          <div className="text-center py-2">
            <span className="text-gray-400 text-xs">等待数据...</span>
          </div>
        )}
      </div>

      {/* Backpack vs Lighter 价差卡片 */}
      <div className="bg-gray-50 rounded p-3 text-sm">
        <div className="flex items-center justify-between mb-2">
          <span className="font-medium text-gray-700">Backpack-Lighter</span>
          {backpackLighterSpread && (
            backpackLighterSpread.isPositive ? (
              <TrendingUp className="h-3 w-3 text-green-500" />
            ) : (
              <TrendingDown className="h-3 w-3 text-red-500" />
            )
          )}
        </div>
        {backpackLighterSpread ? (
          <div className="space-y-1">
            <div className={`text-lg font-bold ${
              backpackLighterSpread.isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              {formatSpread(backpackLighterSpread.absolute)}
            </div>
            <div className="flex items-center gap-2">
              <span className={`text-xs px-1.5 py-0.5 rounded ${
                backpackLighterSpread.isPositive ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}>
                {backpackLighterSpread.isPositive ? '+' : ''}{backpackLighterSpread.percentage.toFixed(2)}%
              </span>
              <span className="text-xs text-gray-500">
                {backpackLighterSpread.isPositive ? 'Backpack更高' : 'Lighter更高'}
              </span>
            </div>
          </div>
        ) : (
          <div className="text-center py-2">
            <span className="text-gray-400 text-xs">等待数据...</span>
          </div>
        )}
      </div>
    </div>
  );
}
