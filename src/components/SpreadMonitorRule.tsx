'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { SpreadMonitorRule, PLATFORMS, OPERATORS } from '@/types/monitor';
import { Trash2, Edit3, Power, PowerOff } from 'lucide-react';

interface SpreadMonitorRuleProps {
  rule: SpreadMonitorRule;
  onUpdate: (rule: SpreadMonitorRule) => void;
  onDelete: (id: string) => void;
  onToggle: (id: string) => void;
  onResetCount: (id: string) => void;
  currentTimer?: number; // 当前计时器秒数
}

export function SpreadMonitorRuleComponent({ rule, onUpdate, onDelete, onToggle, onResetCount, currentTimer }: SpreadMonitorRuleProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editRule, setEditRule] = useState<SpreadMonitorRule>(rule);

  const platformAInfo = PLATFORMS.find(p => p.id === rule.platformA);
  const platformBInfo = PLATFORMS.find(p => p.id === rule.platformB);
  const operatorInfo = OPERATORS.find(op => op.value === rule.operator);

  // 生成规则显示名称
  const getRuleName = (rule: SpreadMonitorRule) => {
    const platformA = PLATFORMS.find(p => p.id === rule.platformA)?.name;
    const platformB = PLATFORMS.find(p => p.id === rule.platformB)?.name;
    const operator = OPERATORS.find(op => op.value === rule.operator)?.label;
    return `${platformA}-${platformB} ${operator} $${rule.threshold}`;
  };

  // 生成规则详细信息
  const getRuleDetails = (rule: SpreadMonitorRule) => {
    const interval = rule.notificationInterval || 5;
    const maxCount = rule.maxNotifications || 10;
    const currentCount = rule.notificationCount || 0;
    const duration = rule.durationSeconds || 0;
    const durationText = duration > 0 ? ` 持续${duration}秒` : ' 立即触发';
    return `间隔${interval}分钟 最多${maxCount}次 已提醒${currentCount}次${durationText}`;
  };

  const handleSave = () => {
    if (editRule.platformA === editRule.platformB) {
      alert('请选择不同的交易所');
      return;
    }
    if (editRule.threshold === undefined || editRule.threshold === null || isNaN(editRule.threshold)) {
      alert('请输入有效的阈值');
      return;
    }
    onUpdate(editRule);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditRule(rule);
    setIsEditing(false);
  };

  if (isEditing) {
    return (
      <div className="bg-gray-50 rounded-lg p-4 border-2 border-blue-200">
        <div className="grid grid-cols-1 md:grid-cols-7 gap-3 items-center">
          {/* 平台A选择 */}
          <div>
            <label className="block text-xs text-gray-600 mb-1">平台A</label>
            <select
              value={editRule.platformA}
              onChange={(e) => setEditRule({...editRule, platformA: e.target.value as any})}
              className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {PLATFORMS.map(platform => (
                <option key={platform.id} value={platform.id}>
                  {platform.name}
                </option>
              ))}
            </select>
          </div>

          {/* 平台B选择 */}
          <div>
            <label className="block text-xs text-gray-600 mb-1">平台B</label>
            <select
              value={editRule.platformB}
              onChange={(e) => setEditRule({...editRule, platformB: e.target.value as any})}
              className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {PLATFORMS.map(platform => (
                <option key={platform.id} value={platform.id}>
                  {platform.name}
                </option>
              ))}
            </select>
          </div>

          {/* 操作符选择 */}
          <div>
            <label className="block text-xs text-gray-600 mb-1">条件</label>
            <select
              value={editRule.operator}
              onChange={(e) => setEditRule({...editRule, operator: e.target.value as any})}
              className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {OPERATORS.map(op => (
                <option key={op.value} value={op.value}>
                  {op.label}
                </option>
              ))}
            </select>
          </div>

          {/* 阈值输入 */}
          <div>
            <label className="block text-xs text-gray-600 mb-1">阈值($)</label>
            <input
              type="number"
              value={editRule.threshold}
              onChange={(e) => setEditRule({...editRule, threshold: parseFloat(e.target.value) || 0})}
              className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="0"
              step="0.01"
            />
          </div>

          {/* 间隔时间 */}
          <div>
            <label className="block text-xs text-gray-600 mb-1">间隔(分钟)</label>
            <input
              type="number"
              value={editRule.notificationInterval}
              onChange={(e) => setEditRule({...editRule, notificationInterval: parseInt(e.target.value) || 5})}
              className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="5"
              min="1"
              step="1"
            />
          </div>

          {/* 最大次数 */}
          <div>
            <label className="block text-xs text-gray-600 mb-1">最大次数</label>
            <input
              type="number"
              value={editRule.maxNotifications}
              onChange={(e) => setEditRule({...editRule, maxNotifications: parseInt(e.target.value) || 10})}
              className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="10"
              min="1"
              step="1"
            />
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-1">
            <Button
              onClick={handleSave}
              size="sm"
              className="h-7 px-2 text-xs bg-green-600 hover:bg-green-700"
            >
              保存
            </Button>
            <Button
              onClick={handleCancel}
              variant="outline"
              size="sm"
              className="h-7 px-2 text-xs"
            >
              取消
            </Button>
          </div>
        </div>

        {/* 持续时间设置 - 单独一行 */}
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 items-end">
            <div>
              <label className="block text-xs text-gray-600 mb-1">持续时间(秒)</label>
              <input
                type="number"
                value={editRule.durationSeconds || 0}
                onChange={(e) => setEditRule({...editRule, durationSeconds: parseInt(e.target.value) || 0})}
                className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0"
                min="0"
                step="1"
              />
            </div>
            <div className="text-xs text-gray-500 md:col-span-2">
              设置为0表示立即触发；大于0表示条件满足并持续指定秒数后才触发提醒
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg p-3 border ${rule.enabled ? 'border-green-200 bg-green-50' : 'border-gray-200'}`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-sm">{getRuleName(rule)}</span>
            {(rule.notificationCount || 0) >= (rule.maxNotifications || 10) && (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                已达上限
              </span>
            )}
          </div>
          <div className="text-sm text-gray-600 mb-1">
            <span className={platformAInfo?.color}>{platformAInfo?.name}</span>
            <span className="mx-1">-</span>
            <span className={platformBInfo?.color}>{platformBInfo?.name}</span>
            <span className="mx-2">{operatorInfo?.label}</span>
            <span className="font-medium">${rule.threshold}</span>
          </div>
          <div className="text-xs text-gray-500">
            {getRuleDetails(rule)}
          </div>

          {/* 计时器显示 */}
          {rule.enabled &&
           (rule.durationSeconds || 0) > 0 &&
           currentTimer !== undefined &&
           (rule.notificationCount || 0) < (rule.maxNotifications || 10) && (
            <div className="text-xs mt-1">
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-blue-600 font-medium">
                    计时中: {currentTimer}s / {rule.durationSeconds}s
                  </span>
                </div>
                <div className="flex-1 bg-gray-200 rounded-full h-1.5">
                  <div
                    className="bg-blue-500 h-1.5 rounded-full transition-all duration-1000"
                    style={{
                      width: `${Math.min((currentTimer / (rule.durationSeconds || 1)) * 100, 100)}%`
                    }}
                  ></div>
                </div>
              </div>
            </div>
          )}

          {rule.lastTriggered && (
            <div className="text-xs text-gray-400 mt-1">
              最后触发: {new Date(rule.lastTriggered).toLocaleString('zh-CN')}
            </div>
          )}
        </div>

        <div className="flex items-center gap-1">
          <Button
            onClick={() => onToggle(rule.id)}
            size="sm"
            className={`h-7 px-3 text-xs ${
              rule.enabled
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-gray-400 hover:bg-gray-500 text-white'
            }`}
          >
            {rule.enabled ? '停用' : '启用'}
          </Button>
          {(rule.notificationCount || 0) > 0 && (
            <Button
              onClick={() => onResetCount(rule.id)}
              variant="outline"
              size="sm"
              className="h-7 px-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50"
              title="重置提醒次数"
            >
              重置
            </Button>
          )}
          <Button
            onClick={() => setIsEditing(true)}
            variant="outline"
            size="sm"
            className="h-7 px-2"
          >
            <Edit3 className="h-3 w-3" />
          </Button>
          <Button
            onClick={() => onDelete(rule.id)}
            variant="outline"
            size="sm"
            className="h-7 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
}
