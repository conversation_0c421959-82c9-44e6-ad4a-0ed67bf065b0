// 新的API格式
export interface NewHistoryRecord {
  timestamp: string;
  binance_price: number;
  backpack_price: number;
  lighter_bid: number;
  lighter_ask: number;
  lighter_mid: number;
  lighter_spread: number;
}

export interface NewHistoryResponse {
  count: number;
  query_type: string;
  query_params: {
    count?: number;
    start_time?: string;
    end_time?: string;
  };
  data: NewHistoryRecord[];
  source: string;
}

// 新的API格式
export interface NewHistoryRecord {
  timestamp: string;
  binance_price: number;
  backpack_price: number;
  lighter_bid: number;
  lighter_ask: number;
  lighter_mid: number;
  lighter_spread: number;
}

export interface NewHistoryResponse {
  count: number;
  query_type: string;
  query_params: {
    count?: number;
    start_time?: string;
    end_time?: string;
  };
  data: NewHistoryRecord[];
  source: string;
}

// 保留旧格式以兼容现有代码
export interface HistoryPriceData {
  exchange: string;
  price: number;
}

export interface HistoryRecord {
  backpack: HistoryPriceData;
  binance: HistoryPriceData;
  lighter: HistoryPriceData;
  timestamp: string;
}

export interface HistoryResponse {
  count: number;
  data: HistoryRecord[];
  format: string;
}
