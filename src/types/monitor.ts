export interface SpreadMonitorRule {
  id: string;
  platformA: 'binance' | 'backpack' | 'lighter';
  platformB: 'binance' | 'backpack' | 'lighter';
  operator: '>=' | '<=' | '=';
  threshold: number;
  enabled: boolean;
  createdAt: string;
  lastTriggered?: string;
  notificationInterval: number; // 提醒间隔时间（分钟）
  maxNotifications: number; // 最大提醒次数
  notificationCount: number; // 当前已提醒次数
  durationSeconds?: number; // 持续时间（秒），可选字段
  conditionStartTime?: string; // 条件开始满足的时间，用于持续时间计算
}

export interface PlatformInfo {
  id: 'binance' | 'backpack' | 'lighter';
  name: string;
  color: string;
}

export const PLATFORMS: PlatformInfo[] = [
  { id: 'binance', name: '币安', color: 'text-yellow-600' },
  { id: 'backpack', name: 'Backpack', color: 'text-purple-600' },
  { id: 'lighter', name: 'Lighter', color: 'text-blue-600' }
];

export const OPERATORS = [
  { value: '>=', label: '大于等于' },
  { value: '<=', label: '小于等于' },
  { value: '=', label: '等于' }
] as const;
