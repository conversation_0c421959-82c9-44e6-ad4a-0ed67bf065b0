'use client';

import { useState, useEffect } from 'react';
import { SimplePriceCard } from '@/components/SimplePriceCard';
import { SpreadDisplay } from '@/components/SpreadDisplay';
import { SettingsModal } from '@/components/SettingsModal';
import { SpreadMonitorManager } from '@/components/SpreadMonitorManager';

import { MultiChartModal } from '@/components/MultiChartModal';
import { PriceData, PriceState } from '@/types/price';
import { fetchLighterPrice, BinanceWebSocket, BackpackWebSocket } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { RefreshCw, Bitcoin, BarChart3 } from 'lucide-react';

export default function Home() {
  const [prices, setPrices] = useState<PriceState>({
    binance: null,
    backpack: null,
    lighter: null,
  });
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [connectionStatus, setConnectionStatus] = useState({
    binance: false,
    backpack: false,
    lighter: false,
  });

  // WebSocket 连接实例
  const [binanceWs, setBinanceWs] = useState<BinanceWebSocket | null>(null);
  const [backpackWs, setBackpackWs] = useState<BackpackWebSocket | null>(null);

  // 更新价格数据的回调函数
  const updatePrice = (exchange: 'binance' | 'backpack' | 'lighter', data: PriceData) => {
    setPrices(prev => ({
      ...prev,
      [exchange]: data,
    }));
    setConnectionStatus(prev => ({
      ...prev,
      [exchange]: data.connected,
    }));
    setLastUpdate(new Date());
  };

  // 获取 Lighter 价格数据
  const fetchLighter = async () => {
    const lighterData = await fetchLighterPrice();
    if (lighterData) {
      updatePrice('lighter', lighterData);
    }
  };

  // 初始化 WebSocket 连接
  useEffect(() => {
    // 初始化币安 WebSocket
    const binanceSocket = new BinanceWebSocket((data) => {
      updatePrice('binance', data);
    });
    binanceSocket.connect();
    setBinanceWs(binanceSocket);

    // 初始化 Backpack WebSocket
    const backpackSocket = new BackpackWebSocket((data) => {
      updatePrice('backpack', data);
    });
    backpackSocket.connect();
    setBackpackWs(backpackSocket);

    // 获取初始 Lighter 数据
    fetchLighter();
    setLoading(false);

    // 设置 Lighter 数据定时更新（每1.2秒）
    const lighterInterval = setInterval(fetchLighter, 1200);

    // 清理函数
    return () => {
      binanceSocket.disconnect();
      backpackSocket.disconnect();
      clearInterval(lighterInterval);
    };
  }, []);

  const handleRefresh = () => {
    setLoading(true);
    fetchLighter();

    // 重新连接 WebSocket
    if (binanceWs) {
      binanceWs.disconnect();
      binanceWs.connect();
    }
    if (backpackWs) {
      backpackWs.disconnect();
      backpackWs.connect();
    }

    setTimeout(() => setLoading(false), 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6 max-w-5xl">
        {/* 紧凑的头部区域 */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bitcoin className="h-5 w-5 text-orange-500" />
              <h1 className="text-lg font-bold text-gray-900">BTC 价格监控</h1>
              <div className="flex items-center gap-3 ml-4 text-xs">
                <div className="flex items-center gap-1">
                  <div className={`w-1.5 h-1.5 rounded-full ${connectionStatus.binance ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-gray-600">币安</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className={`w-1.5 h-1.5 rounded-full ${connectionStatus.backpack ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-gray-600">Backpack</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className={`w-1.5 h-1.5 rounded-full ${connectionStatus.lighter ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-gray-600">Lighter</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500">
                {lastUpdate.toLocaleTimeString('zh-CN')}
              </span>
              <Button
                onClick={handleRefresh}
                variant="outline"
                size="sm"
                disabled={loading}
                className="h-7 px-2 text-xs"
              >
                <RefreshCw className={`h-3 w-3 mr-1 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </Button>

              <MultiChartModal>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-7 px-2"
                >
                  <BarChart3 className="h-3 w-3 mr-1" />
                  图表分析
                </Button>
              </MultiChartModal>
              <SettingsModal />
            </div>
          </div>
        </div>

        {/* 价格和价差一体化显示 */}
        <div className="bg-white rounded-lg shadow-sm p-3 mb-3">
          {/* 价格卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
            <SimplePriceCard
              priceData={prices.binance}
              loading={loading && !prices.binance}
            />
            <SimplePriceCard
              priceData={prices.backpack}
              loading={loading && !prices.backpack}
            />
            <SimplePriceCard
              priceData={prices.lighter}
              loading={loading && !prices.lighter}
            />
          </div>

          {/* 价差显示 - 超紧凑版本 */}
          <div className="border-t border-gray-100 pt-2">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium text-gray-600">价差监控</span>
              <span className="text-xs text-gray-400">vs Lighter</span>
            </div>
            <SpreadDisplay prices={prices} />
          </div>
        </div>



        {/* 价差监控管理器 */}
        <div className="bg-white rounded-lg shadow-sm p-4">
          <SpreadMonitorManager
            binancePrice={prices.binance?.price}
            backpackPrice={prices.backpack?.price}
            lighterPrice={prices.lighter?.price}
          />
        </div>
      </div>
    </div>
  );
}
