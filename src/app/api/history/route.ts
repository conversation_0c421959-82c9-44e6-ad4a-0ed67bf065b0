import { NextResponse } from 'next/server';
import { NewHistoryResponse } from '@/types/history';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const count = searchParams.get('count');
    const startTime = searchParams.get('start_time');
    const endTime = searchParams.get('end_time');

    // 构建查询参数
    const queryParams = new URLSearchParams();
    if (count) {
      queryParams.append('count', count);
    }
    if (startTime) {
      queryParams.append('start_time', startTime);
    }
    if (endTime) {
      queryParams.append('end_time', endTime);
    }

    // 如果没有参数，默认获取1000条
    if (!count && !startTime && !endTime) {
      queryParams.append('count', '1000');
    }

    const response = await fetch(
      `http://47.245.62.204:8080/api/history?${queryParams.toString()}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // 添加超时和缓存控制
        next: { revalidate: 10 }, // 10秒缓存
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: NewHistoryResponse = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching history data:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch history data',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
