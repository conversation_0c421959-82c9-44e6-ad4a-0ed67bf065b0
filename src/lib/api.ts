import { LighterPrice, PriceData } from '@/types/price';

// Lighter API 数据获取
export async function fetchLighterPrice(): Promise<PriceData | null> {
  try {
    const response = await fetch('/api/lighter');
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch Lighter price');
    }
    
    const lighterData: LighterPrice = result.data;
    
    return {
      exchange: 'lighter',
      symbol: 'BTC/USD',
      price: lighterData.mid_price,
      timestamp: new Date(lighterData.timestamp).getTime(),
      connected: lighterData.connected,
    };
  } catch (error) {
    console.error('Error fetching Lighter price:', error);
    return null;
  }
}

// 币安 WebSocket 连接类
export class BinanceWebSocket {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  
  constructor(private onPriceUpdate: (data: PriceData) => void) {}
  
  connect() {
    try {
      // 使用币安期货 WebSocket 获取 BTC/USDC 永续合约价格
      this.ws = new WebSocket('wss://fstream.binance.com/ws/btcusdc@ticker');

      this.ws.onopen = () => {
        console.log('Binance Futures WebSocket connected');
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const priceData: PriceData = {
            exchange: 'binance',
            symbol: 'BTC/USDC (永续)',
            price: parseFloat(data.c),
            timestamp: data.E,
            connected: true,
            change24h: parseFloat(data.p),
            changePercent24h: parseFloat(data.P),
          };
          this.onPriceUpdate(priceData);
        } catch (error) {
          console.error('Error parsing Binance Futures data:', error);
        }
      };
      
      this.ws.onclose = () => {
        console.log('Binance Futures WebSocket disconnected');
        this.handleReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('Binance Futures WebSocket error:', error);
      };
    } catch (error) {
      console.error('Error connecting to Binance Futures WebSocket:', error);
    }
  }
  
  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Attempting to reconnect to Binance Futures (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }
  
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

// Backpack WebSocket 连接类
export class BackpackWebSocket {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor(private onPriceUpdate: (data: PriceData) => void) {}

  connect() {
    try {
      // Backpack WebSocket URL
      this.ws = new WebSocket('wss://ws.backpack.exchange');

      this.ws.onopen = () => {
        console.log('Backpack Futures WebSocket connected');
        this.reconnectAttempts = 0;

        // 订阅 BTC_USDC_PERP ticker (永续合约)
        const subscribeMessage = {
          method: 'SUBSCRIBE',
          params: ['ticker.BTC_USDC_PERP']
        };
        this.ws?.send(JSON.stringify(subscribeMessage));
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);

          // 调试：打印所有接收到的消息
          console.log('🔍 Backpack WebSocket message:', message);

          // 处理 ticker 数据
          if (message.stream === 'ticker.BTC_USDC_PERP' && message.data) {
            const tickerData = message.data;
            console.log('📊 Backpack Futures ticker data:', {
              stream: message.stream,
              price: tickerData.c,
              symbol: 'BTC_USDC_PERP',
              timestamp: tickerData.E,
              rawData: tickerData
            });

            const priceData: PriceData = {
              exchange: 'backpack',
              symbol: 'BTC/USDC (永续)',
              price: parseFloat(tickerData.c), // 最新价格
              timestamp: tickerData.E ? Math.floor(tickerData.E / 1000) : Date.now(), // 转换微秒到毫秒
              connected: true,
              changePercent24h: tickerData.o && tickerData.c ?
                ((parseFloat(tickerData.c) - parseFloat(tickerData.o)) / parseFloat(tickerData.o)) * 100 : undefined,
            };
            this.onPriceUpdate(priceData);
          } else {
            console.log('⚠️ Backpack message not matching expected format:', {
              stream: message.stream,
              hasData: !!message.data,
              messageType: typeof message
            });
          }
        } catch (error) {
          console.error('Error parsing Backpack Futures data:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('Backpack Futures WebSocket disconnected');
        this.handleReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('Backpack Futures WebSocket error:', error);
      };
    } catch (error) {
      console.error('Error connecting to Backpack Futures WebSocket:', error);
    }
  }
  
  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Attempting to reconnect to Backpack Futures (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }
  
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
