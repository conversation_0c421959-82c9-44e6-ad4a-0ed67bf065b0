# BTC 价格监控 - Watch View

一个基于 Next.js 和 shadcn/ui 构建的实时 BTC 价格监控应用，支持多个交易所的价格对比。

## 功能特性

- 🚀 **实时价格更新**: 通过 WebSocket 连接获取实时价格数据
- 📊 **多交易所支持**:
  - 币安 (Binance) - BTC/USDC
  - Backpack - BTC/USDC
  - Lighter - BTC/USD
- 💎 **现代化 UI**: 使用 shadcn/ui 组件库构建美观界面
- 📱 **响应式设计**: 支持桌面端和移动端
- 🔄 **自动重连**: WebSocket 连接断开时自动重连
- 📈 **价格统计**: 显示最高价、最低价和价差
- 🟢 **连接状态**: 实时显示各交易所连接状态

## 技术栈

- **前端框架**: Next.js 15 (App Router)
- **UI 组件**: shadcn/ui + Tailwind CSS
- **语言**: TypeScript
- **图标**: Lucide React
- **实时数据**: WebSocket + REST API

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

应用将在 [http://localhost:3000](http://localhost:3000) 启动。

### 构建生产版本

```bash
npm run build
npm start
```

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API 路由
│   │   └── lighter/       # Lighter 价格 API
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx          # 主页面
├── components/            # React 组件
│   ├── ui/               # shadcn/ui 组件
│   ├── PriceCard.tsx     # 价格卡片组件
│   └── ConnectionStatus.tsx # 连接状态组件
├── lib/                  # 工具函数
│   ├── api.ts           # API 和 WebSocket 连接
│   └── utils.ts         # 通用工具函数
└── types/               # TypeScript 类型定义
    └── price.ts         # 价格数据类型
```

## API 接口

### Lighter API
- **端点**: `http://*************:8080/api/lighter`
- **方法**: GET
- **返回**: BTC 合约价格数据

### WebSocket 连接
- **币安**: `wss://stream.binance.com:9443/ws/btcusdc@ticker`
- **Backpack**: `wss://ws.backpack.exchange` (ticker.BTC_USDC)

## 数据更新频率

- **币安**: 实时 WebSocket 推送
- **Backpack**: 实时 WebSocket 推送
- **Lighter**: 每 5 秒轮询一次

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 开发说明

### 添加新的交易所

1. 在 `src/types/price.ts` 中添加新的交易所类型
2. 在 `src/lib/api.ts` 中创建对应的 WebSocket 类
3. 在主页面中集成新的数据源
4. 更新 UI 组件以显示新的交易所数据

### 自定义样式

项目使用 Tailwind CSS，可以在 `tailwind.config.js` 中自定义主题。

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
# watch_view
